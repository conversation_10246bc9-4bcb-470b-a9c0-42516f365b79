<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VMUrugan Admin Portal - Real Time Data</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #333;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: bold;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.8;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #FFD700;
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #666;
            font-size: 1.1em;
        }
        
        .content {
            padding: 30px;
        }
        
        .section-title {
            font-size: 1.8em;
            margin-bottom: 20px;
            color: #333;
            border-bottom: 3px solid #FFD700;
            padding-bottom: 10px;
        }
        
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
            transform: translateY(-2px);
        }
        
        .btn-warning {
            background: #ffc107;
            color: #333;
        }
        
        .btn-warning:hover {
            background: #e0a800;
            transform: translateY(-2px);
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th {
            background: #343a40;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }
        
        td {
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
        }
        
        .status-real {
            background: #d4edda;
            color: #155724;
        }
        
        .status-test {
            background: #fff3cd;
            color: #856404;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        
        .log-info {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .log-success {
            background: #d4edda;
            color: #155724;
        }
        
        .log-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-state h3 {
            font-size: 1.5em;
            margin-bottom: 15px;
        }
        
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            color: #666;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin-right: 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏆 VMUrugan Admin Portal</h1>
            <p>Real-Time Customer Data Dashboard</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalCustomers">0</div>
                <div class="stat-label">Total Customers</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="realCustomers">0</div>
                <div class="stat-label">Real Customers</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="testCustomers">0</div>
                <div class="stat-label">Test Data</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="lastUpdate">Never</div>
                <div class="stat-label">Last Updated</div>
            </div>
        </div>
        
        <div class="content">
            <h2 class="section-title">📊 Customer Management</h2>
            
            <div class="controls">
                <button class="btn btn-primary" onclick="loadCustomers()">🔄 Refresh Data</button>
                <button class="btn btn-success" onclick="testConnection()">🔗 Test Connection</button>
                <button class="btn btn-warning" onclick="clearLog()">🗑️ Clear Log</button>
                <label style="display: flex; align-items: center; gap: 8px; margin-left: auto;">
                    <input type="checkbox" id="showTestData" onchange="toggleTestData()">
                    <span>Show Test Data</span>
                </label>
            </div>
            
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Phone</th>
                            <th>Email</th>
                            <th>Registration Date</th>
                            <th>Type</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody id="customersTable">
                        <tr>
                            <td colspan="6" class="loading">
                                <div class="spinner"></div>
                                Loading customer data...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="log" id="activityLog">
                <div class="log-entry log-info">🚀 New Admin Portal initialized...</div>
                <div class="log-entry log-info">📊 Project: vmurugan-gold-trading-2025</div>
                <div class="log-entry log-info">⏰ Ready to load real-time data</div>
            </div>
        </div>
    </div>

    <script>
        const CONFIG = {
            projectId: 'vmurugan-gold-trading',
            apiKey: 'AIzaSyCaS4pdX3a_JFdL0PolTHYnpebg5ppbgs0',
            endpoints: [
                'https://firestore.googleapis.com/v1/projects/vmurugan-gold-trading/databases/(default)/documents/customers',
                'https://vmurugan-gold-trading-default-rtdb.firebaseio.com/customers.json',
                'https://vmurugan-gold-trading-default-rtdb.asia-southeast1.firebaseio.com/customers.json'
            ]
        };

        let allCustomers = [];
        let showTestDataFlag = false;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('activityLog');
            const timestamp = new Date().toLocaleTimeString();
            const logClass = `log-${type}`;
            
            const entry = document.createElement('div');
            entry.className = `log-entry ${logClass}`;
            entry.textContent = `[${timestamp}] ${message}`;
            
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('activityLog').innerHTML = '';
            log('🗑️ Activity log cleared');
        }

        function updateStats(customers) {
            const realCustomers = customers.filter(c => !isTestData(c));
            const testCustomers = customers.filter(c => isTestData(c));
            
            document.getElementById('totalCustomers').textContent = customers.length;
            document.getElementById('realCustomers').textContent = realCustomers.length;
            document.getElementById('testCustomers').textContent = testCustomers.length;
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
        }

        function isTestData(customer) {
            const phone = customer.phone || '';
            const name = customer.name || '';
            const email = customer.email || '';
            
            return phone.includes('TEST_') || 
                   phone.includes('test') || 
                   phone.includes('9999999999') ||
                   phone.includes('1234567890') ||
                   name.includes('Test') ||
                   name.includes('Demo') ||
                   email.includes('test@') ||
                   email.includes('@example.');
        }

        function displayCustomers(customers) {
            const tbody = document.getElementById('customersTable');
            
            const filteredCustomers = showTestDataFlag ? customers : customers.filter(c => !isTestData(c));
            
            if (filteredCustomers.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="empty-state">
                            <h3>📱 No ${showTestDataFlag ? '' : 'Real '}Customer Data Found</h3>
                            <p>${showTestDataFlag ? 'No data in Firebase yet.' : 'No real customer registrations yet. Test data is hidden.'}</p>
                            <p>Data will appear here when customers register through the mobile app.</p>
                        </td>
                    </tr>
                `;
                return;
            }
            
            tbody.innerHTML = filteredCustomers.map(customer => {
                const isTest = isTestData(customer);
                const statusClass = isTest ? 'status-test' : 'status-real';
                const statusText = isTest ? 'Test Data' : 'Real Customer';
                
                return `
                    <tr>
                        <td><strong>${customer.name || 'Unknown'}</strong></td>
                        <td>${customer.phone || 'Unknown'}</td>
                        <td>${customer.email || 'Unknown'}</td>
                        <td>${customer.registration_date ? new Date(customer.registration_date).toLocaleDateString() : 'Unknown'}</td>
                        <td>${customer.data_type || 'unknown'}</td>
                        <td><span class="status ${statusClass}">${statusText}</span></td>
                    </tr>
                `;
            }).join('');
        }

        function toggleTestData() {
            showTestDataFlag = document.getElementById('showTestData').checked;
            log(`🔄 ${showTestDataFlag ? 'Showing' : 'Hiding'} test data`);
            displayCustomers(allCustomers);
            updateStats(allCustomers);
        }

        async function testConnection() {
            log('🔗 Testing Firebase connection...', 'info');
            
            for (let i = 0; i < CONFIG.endpoints.length; i++) {
                const endpoint = CONFIG.endpoints[i];
                log(`📡 Testing endpoint ${i + 1}...`);
                
                try {
                    const url = endpoint.includes('firebaseio.com') 
                        ? `${endpoint}?auth=${CONFIG.apiKey}`
                        : `${endpoint}?key=${CONFIG.apiKey}`;
                    
                    const response = await fetch(url);
                    
                    if (response.ok) {
                        log(`✅ Endpoint ${i + 1} SUCCESS (${response.status})`, 'success');
                        return true;
                    } else {
                        log(`❌ Endpoint ${i + 1} FAILED (${response.status})`, 'error');
                    }
                } catch (error) {
                    log(`❌ Endpoint ${i + 1} ERROR: ${error.message}`, 'error');
                }
            }
            
            log('❌ All connection tests failed', 'error');
            return false;
        }

        async function loadCustomers() {
            log('🔄 Loading customer data...', 'info');
            
            const tbody = document.getElementById('customersTable');
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="loading">
                        <div class="spinner"></div>
                        Loading fresh data from Firebase...
                    </td>
                </tr>
            `;
            
            for (let i = 0; i < CONFIG.endpoints.length; i++) {
                const endpoint = CONFIG.endpoints[i];
                log(`📡 Trying endpoint ${i + 1}...`);
                
                try {
                    const url = endpoint.includes('firebaseio.com') 
                        ? `${endpoint}?auth=${CONFIG.apiKey}`
                        : `${endpoint}?key=${CONFIG.apiKey}`;
                    
                    const response = await fetch(url);
                    
                    if (response.ok) {
                        const data = await response.json();
                        
                        if (endpoint.includes('firebaseio.com')) {
                            // Realtime Database format
                            if (data && typeof data === 'object') {
                                allCustomers = Object.entries(data).map(([key, value]) => ({
                                    id: key,
                                    ...value
                                }));
                            } else {
                                allCustomers = [];
                            }
                        } else {
                            // Firestore format
                            const documents = data.documents || [];
                            allCustomers = documents.map(doc => {
                                const fields = doc.fields || {};
                                return {
                                    id: doc.name.split('/').pop(),
                                    name: fields.name?.stringValue || '',
                                    phone: fields.phone?.stringValue || '',
                                    email: fields.email?.stringValue || '',
                                    address: fields.address?.stringValue || '',
                                    pan_card: fields.pan_card?.stringValue || '',
                                    registration_date: fields.registration_date?.stringValue || '',
                                    data_type: fields.data_type?.stringValue || 'unknown'
                                };
                            });
                        }
                        
                        log(`✅ SUCCESS! Found ${allCustomers.length} total records`, 'success');
                        displayCustomers(allCustomers);
                        updateStats(allCustomers);
                        return;
                    } else {
                        log(`❌ Endpoint ${i + 1} failed: ${response.status}`, 'error');
                    }
                } catch (error) {
                    log(`❌ Endpoint ${i + 1} error: ${error.message}`, 'error');
                }
            }
            
            log('❌ All endpoints failed - no data loaded', 'error');
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="empty-state">
                        <h3>❌ Connection Failed</h3>
                        <p>Unable to connect to Firebase. Check the activity log for details.</p>
                        <p>Try clicking "Test Connection" to diagnose the issue.</p>
                    </td>
                </tr>
            `;
        }

        // Auto-refresh every 30 seconds
        setInterval(loadCustomers, 30000);

        // Load data when page loads
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 New Admin Portal loaded successfully!', 'success');
            setTimeout(loadCustomers, 1000);
        });
    </script>
</body>
</html>
