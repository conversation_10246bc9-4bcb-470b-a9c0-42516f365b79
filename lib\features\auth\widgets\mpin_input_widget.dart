import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class MPinInputWidget extends StatefulWidget {
  final Function(String) onMPinComplete;
  final String title;
  final String subtitle;
  final bool isConfirmation;

  const MPinInputWidget({
    super.key,
    required this.onMPinComplete,
    this.title = 'Create MPIN',
    this.subtitle = 'Enter 4-digit MPIN for secure login',
    this.isConfirmation = false,
  });

  @override
  State<MPinInputWidget> createState() => _MPinInputWidgetState();
}

class _MPinInputWidgetState extends State<MPinInputWidget> {
  final List<TextEditingController> _controllers = List.generate(4, (_) => TextEditingController());
  final List<FocusNode> _focusNodes = List.generate(4, (_) => FocusNode());
  String _mpin = '';

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var node in _focusNodes) {
      node.dispose();
    }
    super.dispose();
  }

  void _onDigitChanged(int index, String value) {
    if (value.isNotEmpty) {
      _controllers[index].text = value;
      if (index < 3) {
        _focusNodes[index + 1].requestFocus();
      } else {
        _focusNodes[index].unfocus();
      }
    } else if (index > 0) {
      _focusNodes[index - 1].requestFocus();
    }

    _updateMPin();
  }

  void _updateMPin() {
    _mpin = _controllers.map((c) => c.text).join();
    if (_mpin.length == 4) {
      widget.onMPinComplete(_mpin);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          widget.title,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          widget.subtitle,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 32),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: List.generate(4, (index) => _buildMPinField(index)),
        ),
      ],
    );
  }

  Widget _buildMPinField(int index) {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFFFD700), width: 2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: TextFormField(
        controller: _controllers[index],
        focusNode: _focusNodes[index],
        textAlign: TextAlign.center,
        keyboardType: TextInputType.number,
        obscureText: true,
        maxLength: 1,
        style: const TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
        ),
        decoration: const InputDecoration(
          counterText: '',
          border: InputBorder.none,
        ),
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
        ],
        onChanged: (value) => _onDigitChanged(index, value),
      ),
    );
  }
}