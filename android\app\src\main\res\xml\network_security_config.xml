<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- Allow HTTP connections to local development server -->
    <domain-config cleartextTrafficPermitted="true">
        <!-- Your computer's IP address for local backend -->
        <domain includeSubdomains="false">************</domain>
        
        <!-- Common local development IPs -->
        <domain includeSubdomains="false">localhost</domain>
        <domain includeSubdomains="false">127.0.0.1</domain>
        <domain includeSubdomains="false">********</domain>
        
        <!-- Common local network ranges -->
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">10.0.0.0</domain>
    </domain-config>
    
    <!-- Default security for all other domains -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>
