{"info": {"name": "VMUrugan Gold Trading API Tests", "description": "Comprehensive API testing for VMUrugan Gold Trading Platform Backend", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "2.0.0"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000/api", "type": "string"}, {"key": "healthUrl", "value": "http://localhost:3000/health", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}, {"key": "adminToken", "value": "", "type": "string"}, {"key": "customerId", "value": "", "type": "string"}, {"key": "transactionId", "value": "", "type": "string"}, {"key": "schemeId", "value": "", "type": "string"}], "item": [{"name": "Health Check", "item": [{"name": "Server Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{healthUrl}}", "host": ["{{healthUrl}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Server is healthy', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('OK');", "    pm.expect(response.service).to.include('VMUrugan');", "});"]}}]}]}, {"name": "Authentication", "item": [{"name": "Register New Customer", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"**********\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"postman123\",\n  \"name\": \"Postman Test User\",\n  \"address\": \"Postman Test Address, Chennai, Tamil Nadu\",\n  \"panCard\": \"**********\",\n  \"deviceId\": \"postman-test-device\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register", "host": ["{{baseUrl}}"], "path": ["auth", "register"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Registration successful', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data).to.have.property('accessToken');", "    pm.expect(response.data).to.have.property('customerId');", "    ", "    // Store tokens and customer ID for subsequent requests", "    pm.collectionVariables.set('authToken', response.data.accessToken);", "    pm.collectionVariables.set('customerId', response.data.customerId);", "});"]}}]}, {"name": "<PERSON>gin Customer", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"**********\",\n  \"password\": \"postman123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Login successful', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data).to.have.property('accessToken');", "    pm.expect(response.data.role).to.eql('customer');", "    ", "    // Update auth token", "    pm.collectionVariables.set('authToken', response.data.accessToken);", "});"]}}]}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"9999999999\",\n  \"password\": \"VMURUGAN_ADMIN_2025\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Admin login successful', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data.role).to.eql('admin');", "    ", "    // Store admin token", "    pm.collectionVariables.set('adminToken', response.data.accessToken);", "});"]}}]}, {"name": "Validate Customer", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/customers/validate/**********", "host": ["{{baseUrl}}"], "path": ["customers", "validate", "**********"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Customer validation successful', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data).to.have.property('customer_id');", "});"]}}]}, {"name": "Get Customer Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/customers/profile", "host": ["{{baseUrl}}"], "path": ["customers", "profile"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Profile retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data).to.have.property('customer_id');", "    pm.expect(response.data).to.have.property('name');", "});"]}}]}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/auth/logout", "host": ["{{baseUrl}}"], "path": ["auth", "logout"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Logout successful', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "});"]}}]}]}, {"name": "Transactions", "item": [{"name": "Create Transaction", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customerId\": \"{{customerId}}\",\n  \"type\": \"BUY\",\n  \"amount\": 10000.00,\n  \"goldGrams\": 1.5,\n  \"goldPricePerGram\": 6666.67,\n  \"paymentMethod\": \"UPI\",\n  \"gatewayTransactionId\": \"POSTMAN_TEST_123\",\n  \"deviceInfo\": \"Postman Test Device\",\n  \"location\": \"Chennai, Tamil Nadu\",\n  \"notes\": \"Postman test transaction\"\n}"}, "url": {"raw": "{{baseUrl}}/transactions", "host": ["{{baseUrl}}"], "path": ["transactions"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Transaction created successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data).to.have.property('transactionId');", "    pm.expect(response.data.status).to.eql('PENDING');", "    ", "    // Store transaction ID", "    pm.collectionVariables.set('transactionId', response.data.transactionId);", "});"]}}]}, {"name": "Update Transaction Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"SUCCESS\",\n  \"gatewayTransactionId\": \"GATEWAY_SUCCESS_POSTMAN\"\n}"}, "url": {"raw": "{{baseUrl}}/transactions/{{transactionId}}/status", "host": ["{{baseUrl}}"], "path": ["transactions", "{{transactionId}}", "status"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Transaction status updated', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data.status).to.eql('SUCCESS');", "});"]}}]}, {"name": "Get Customer Transactions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/transactions/customer/{{customerId}}?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["transactions", "customer", "{{customerId}}"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Transactions retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data).to.have.property('transactions');", "    pm.expect(response.data).to.have.property('pagination');", "});"]}}]}, {"name": "Get Transaction by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/transactions/{{transactionId}}", "host": ["{{baseUrl}}"], "path": ["transactions", "{{transactionId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Transaction retrieved by ID', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data.transaction_id).to.eql(pm.collectionVariables.get('transactionId'));", "});"]}}]}]}, {"name": "Schemes", "item": [{"name": "Create Scheme", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customerId\": \"{{customerId}}\",\n  \"schemeType\": \"monthly_sip\",\n  \"monthlyAmount\": 5000.00,\n  \"durationMonths\": 12,\n  \"startDate\": \"2025-08-01T00:00:00.000Z\"\n}"}, "url": {"raw": "{{baseUrl}}/schemes", "host": ["{{baseUrl}}"], "path": ["schemes"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Scheme created successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data).to.have.property('schemeId');", "    pm.expect(response.data.status).to.eql('active');", "    ", "    // Store scheme ID", "    pm.collectionVariables.set('schemeId', response.data.schemeId);", "});"]}}]}, {"name": "Get Customer Schemes", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/schemes/customer/{{customerId}}", "host": ["{{baseUrl}}"], "path": ["schemes", "customer", "{{customerId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Schemes retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(Array.isArray(response.data)).to.be.true;", "});"]}}]}, {"name": "Process Scheme Payment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 5000.00,\n  \"goldGrams\": 0.75,\n  \"goldPricePerGram\": 6666.67,\n  \"paymentMethod\": \"UPI\",\n  \"gatewayTransactionId\": \"SCHEME_PAYMENT_POSTMAN\"\n}"}, "url": {"raw": "{{baseUrl}}/schemes/{{schemeId}}/payment", "host": ["{{baseUrl}}"], "path": ["schemes", "{{schemeId}}", "payment"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Scheme payment processed', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data.paidMonths).to.be.greaterThan(0);", "});"]}}]}]}, {"name": "Admin", "item": [{"name": "Get Admin Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/admin/dashboard", "host": ["{{baseUrl}}"], "path": ["admin", "dashboard"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Admin dashboard retrieved', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data).to.have.property('stats');", "});"]}}]}, {"name": "Get All Customers", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/admin/customers?page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["admin", "customers"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Admin customers retrieved', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data).to.have.property('customers');", "    pm.expect(response.data).to.have.property('pagination');", "});"]}}]}, {"name": "Get All Transactions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/admin/transactions?page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["admin", "transactions"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Admin transactions retrieved', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data).to.have.property('transactions');", "    pm.expect(response.data).to.have.property('pagination');", "});"]}}]}]}, {"name": "Analytics", "item": [{"name": "Log Analytics Event", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"event\": \"postman_test_event\",\n  \"data\": {\n    \"test_type\": \"integration\",\n    \"timestamp\": \"2025-07-29T12:00:00Z\",\n    \"user_agent\": \"Postman\",\n    \"test_result\": \"success\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/analytics", "host": ["{{baseUrl}}"], "path": ["analytics"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Analytics event logged', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "});"]}}]}]}]}