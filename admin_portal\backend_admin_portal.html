<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VMUrugan Admin Portal - Backend API</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
        }

        .header .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 16px;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            max-width: 400px;
            margin: 50px auto;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .dashboard {
            display: none;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            text-align: center;
        }

        .stat-card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 18px;
        }

        .stat-card .value {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-card .label {
            color: #7f8c8d;
            font-size: 14px;
        }

        .data-section {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin-bottom: 20px;
        }

        .data-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e0e0e0;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
        }

        .status.pending {
            background: #fff3cd;
            color: #856404;
        }

        .status.failed {
            background: #f8d7da;
            color: #721c24;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .nav-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }

        .nav-tab {
            padding: 12px 24px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            color: #7f8c8d;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .nav-tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .logout-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #e74c3c;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }

        .refresh-btn {
            background: #27ae60;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-bottom: 15px;
        }

        .connection-status {
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .connection-status.connected {
            background: #d4edda;
            color: #155724;
        }

        .connection-status.disconnected {
            background: #f8d7da;
            color: #721c24;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .table {
                font-size: 14px;
            }
            
            .nav-tabs {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div id="loginScreen">
        <div class="login-container">
            <div class="header">
                <h1>🏆 VMUrugan Admin Portal</h1>
                <p class="subtitle">Backend API Management Dashboard</p>
            </div>
            
            <div id="connectionStatus" class="connection-status disconnected">
                🔴 Checking backend connection...
            </div>
            
            <form id="loginForm">
                <div class="form-group">
                    <label for="phone">Admin Phone</label>
                    <input type="tel" id="phone" value="9999999999" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Admin Password</label>
                    <input type="password" id="password" value="VMURUGAN_ADMIN_2025" required>
                </div>
                
                <button type="submit" class="btn" id="loginBtn">
                    Login to Admin Portal
                </button>
            </form>
            
            <div id="loginError" class="error" style="display: none;"></div>
            
            <div style="margin-top: 20px; padding: 15px; background: #e8f4f8; border-radius: 8px;">
                <h4>🔧 Backend API Setup</h4>
                <p><strong>Server:</strong> http://localhost:3000</p>
                <p><strong>Health Check:</strong> <a href="http://localhost:3000/health" target="_blank">Test Connection</a></p>
                <p><strong>Setup:</strong> Run <code>npm run dev</code> in server folder</p>
            </div>
        </div>
    </div>

    <!-- Dashboard -->
    <div id="dashboard" class="dashboard">
        <div class="container">
            <button class="logout-btn" onclick="logout()">Logout</button>
            
            <div class="header">
                <h1>🏆 VMUrugan Admin Dashboard</h1>
                <p class="subtitle">Backend API Management - Real-time Business Analytics</p>
            </div>
            
            <div id="dashboardConnectionStatus" class="connection-status connected">
                🟢 Connected to Backend API
            </div>
            
            <button class="refresh-btn" onclick="refreshDashboard()">🔄 Refresh Data</button>
            
            <!-- Statistics Cards -->
            <div class="stats-grid" id="statsGrid">
                <div class="stat-card">
                    <h3>Total Customers</h3>
                    <div class="value" id="totalCustomers">-</div>
                    <div class="label">Registered Users</div>
                </div>
                <div class="stat-card">
                    <h3>Total Transactions</h3>
                    <div class="value" id="totalTransactions">-</div>
                    <div class="label">All Time</div>
                </div>
                <div class="stat-card">
                    <h3>Total Revenue</h3>
                    <div class="value" id="totalRevenue">₹-</div>
                    <div class="label">Successful Transactions</div>
                </div>
                <div class="stat-card">
                    <h3>Gold Sold</h3>
                    <div class="value" id="totalGoldSold">-g</div>
                    <div class="label">Total Weight</div>
                </div>
                <div class="stat-card">
                    <h3>Active Schemes</h3>
                    <div class="value" id="activeSchemes">-</div>
                    <div class="label">Investment Plans</div>
                </div>
                <div class="stat-card">
                    <h3>Today's Revenue</h3>
                    <div class="value" id="todayRevenue">₹-</div>
                    <div class="label">Today's Earnings</div>
                </div>
            </div>

            <!-- Navigation Tabs -->
            <div class="nav-tabs">
                <button class="nav-tab active" onclick="showTab('customers')">👥 Customers</button>
                <button class="nav-tab" onclick="showTab('transactions')">💰 Transactions</button>
                <button class="nav-tab" onclick="showTab('schemes')">📋 Schemes</button>
                <button class="nav-tab" onclick="showTab('analytics')">📊 Analytics</button>
            </div>

            <!-- Customers Tab -->
            <div id="customersTab" class="tab-content active">
                <div class="data-section">
                    <h2>👥 Customer Management</h2>
                    <div id="customersLoading" class="loading">Loading customers...</div>
                    <div id="customersError" class="error" style="display: none;"></div>
                    <div id="customersContent" style="display: none;">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Customer ID</th>
                                    <th>Name</th>
                                    <th>Phone</th>
                                    <th>Email</th>
                                    <th>Total Invested</th>
                                    <th>Gold Holdings</th>
                                    <th>Transactions</th>
                                    <th>Registration Date</th>
                                </tr>
                            </thead>
                            <tbody id="customersTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Transactions Tab -->
            <div id="transactionsTab" class="tab-content">
                <div class="data-section">
                    <h2>💰 Transaction Management</h2>
                    <div id="transactionsLoading" class="loading">Loading transactions...</div>
                    <div id="transactionsError" class="error" style="display: none;"></div>
                    <div id="transactionsContent" style="display: none;">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Transaction ID</th>
                                    <th>Customer</th>
                                    <th>Type</th>
                                    <th>Amount</th>
                                    <th>Gold (grams)</th>
                                    <th>Status</th>
                                    <th>Payment Method</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody id="transactionsTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Schemes Tab -->
            <div id="schemesTab" class="tab-content">
                <div class="data-section">
                    <h2>📋 Scheme Management</h2>
                    <div id="schemesLoading" class="loading">Loading schemes...</div>
                    <div id="schemesError" class="error" style="display: none;"></div>
                    <div id="schemesContent" style="display: none;">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Scheme ID</th>
                                    <th>Customer</th>
                                    <th>Type</th>
                                    <th>Monthly Amount</th>
                                    <th>Duration</th>
                                    <th>Progress</th>
                                    <th>Gold Accumulated</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody id="schemesTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Analytics Tab -->
            <div id="analyticsTab" class="tab-content">
                <div class="data-section">
                    <h2>📊 Business Analytics</h2>
                    <div id="analyticsContent">
                        <div class="stats-grid">
                            <div class="stat-card">
                                <h3>Monthly Growth</h3>
                                <div class="value" id="monthlyGrowth">-</div>
                                <div class="label">Revenue Increase</div>
                            </div>
                            <div class="stat-card">
                                <h3>Customer Retention</h3>
                                <div class="value" id="customerRetention">-</div>
                                <div class="label">Active Customers</div>
                            </div>
                            <div class="stat-card">
                                <h3>Average Transaction</h3>
                                <div class="value" id="avgTransaction">₹-</div>
                                <div class="label">Per Transaction</div>
                            </div>
                            <div class="stat-card">
                                <h3>Success Rate</h3>
                                <div class="value" id="successRate">-</div>
                                <div class="label">Transaction Success</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Backend API Configuration
        const API_CONFIG = {
            baseUrl: 'http://localhost:3000/api',
            healthUrl: 'http://localhost:3000/health',
            timeout: 30000
        };

        let authToken = null;
        let refreshInterval = null;

        // Check backend connection
        async function checkBackendConnection() {
            try {
                const response = await fetch(API_CONFIG.healthUrl, {
                    method: 'GET',
                    timeout: 5000
                });

                if (response.ok) {
                    const data = await response.json();
                    updateConnectionStatus(true, `Connected to ${data.service} v${data.version || '2.0.0'}`);
                    return true;
                } else {
                    updateConnectionStatus(false, 'Backend server responded with error');
                    return false;
                }
            } catch (error) {
                updateConnectionStatus(false, 'Cannot connect to backend server');
                return false;
            }
        }

        // Update connection status
        function updateConnectionStatus(connected, message) {
            const statusElements = [
                document.getElementById('connectionStatus'),
                document.getElementById('dashboardConnectionStatus')
            ];

            statusElements.forEach(element => {
                if (element) {
                    element.className = `connection-status ${connected ? 'connected' : 'disconnected'}`;
                    element.innerHTML = `${connected ? '🟢' : '🔴'} ${message}`;
                }
            });
        }

        // Make API request
        async function makeApiRequest(endpoint, options = {}) {
            try {
                const url = `${API_CONFIG.baseUrl}${endpoint}`;
                const headers = {
                    'Content-Type': 'application/json',
                    ...options.headers
                };

                if (authToken) {
                    headers['Authorization'] = `Bearer ${authToken}`;
                }

                const response = await fetch(url, {
                    method: options.method || 'GET',
                    headers,
                    body: options.body ? JSON.stringify(options.body) : undefined,
                    timeout: API_CONFIG.timeout
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.message || `HTTP ${response.status}`);
                }

                return data;
            } catch (error) {
                console.error('API Request failed:', error);
                throw error;
            }
        }

        // Login functionality
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const phone = document.getElementById('phone').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const errorDiv = document.getElementById('loginError');

            loginBtn.disabled = true;
            loginBtn.textContent = 'Logging in...';
            errorDiv.style.display = 'none';

            try {
                // Check backend connection first
                const connected = await checkBackendConnection();
                if (!connected) {
                    throw new Error('Backend server is not available. Please ensure the server is running.');
                }

                // Attempt login
                const response = await makeApiRequest('/auth/login', {
                    method: 'POST',
                    body: { phone, password }
                });

                if (response.success && response.data) {
                    authToken = response.data.accessToken;
                    
                    // Check if user is admin
                    if (response.data.role !== 'admin') {
                        throw new Error('Admin access required');
                    }

                    // Hide login screen and show dashboard
                    document.getElementById('loginScreen').style.display = 'none';
                    document.getElementById('dashboard').style.display = 'block';
                    
                    // Load dashboard data
                    await loadDashboardData();
                    
                    // Start auto-refresh
                    startAutoRefresh();
                } else {
                    throw new Error(response.message || 'Login failed');
                }
            } catch (error) {
                errorDiv.textContent = error.message;
                errorDiv.style.display = 'block';
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = 'Login to Admin Portal';
            }
        });

        // Load dashboard data
        async function loadDashboardData() {
            try {
                const response = await makeApiRequest('/admin/dashboard');
                
                if (response.success && response.data) {
                    updateDashboardStats(response.data.stats);
                    updateCustomersTable(response.data.customers || []);
                    updateTransactionsTable(response.data.recent_transactions || []);
                } else {
                    throw new Error(response.message || 'Failed to load dashboard data');
                }
            } catch (error) {
                console.error('Dashboard load error:', error);
                showError('Failed to load dashboard data: ' + error.message);
            }
        }

        // Update dashboard statistics
        function updateDashboardStats(stats) {
            document.getElementById('totalCustomers').textContent = stats.total_customers || 0;
            document.getElementById('totalTransactions').textContent = stats.total_transactions || 0;
            document.getElementById('totalRevenue').textContent = `₹${formatNumber(stats.total_revenue || 0)}`;
            document.getElementById('totalGoldSold').textContent = `${(stats.total_gold_sold || 0).toFixed(2)}g`;
            document.getElementById('activeSchemes').textContent = stats.active_schemes || 0;
            document.getElementById('todayRevenue').textContent = `₹${formatNumber(stats.revenue_today || 0)}`;
        }

        // Update customers table
        function updateCustomersTable(customers) {
            const tbody = document.getElementById('customersTableBody');
            tbody.innerHTML = '';

            customers.forEach(customer => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${customer.customer_id}</td>
                    <td>${customer.name}</td>
                    <td>${customer.phone}</td>
                    <td>${customer.email}</td>
                    <td>₹${formatNumber(customer.total_invested || 0)}</td>
                    <td>${(customer.total_gold || 0).toFixed(3)}g</td>
                    <td>${customer.transaction_count || 0}</td>
                    <td>${formatDate(customer.registration_date)}</td>
                `;
            });

            document.getElementById('customersLoading').style.display = 'none';
            document.getElementById('customersContent').style.display = 'block';
        }

        // Update transactions table
        function updateTransactionsTable(transactions) {
            const tbody = document.getElementById('transactionsTableBody');
            tbody.innerHTML = '';

            transactions.forEach(transaction => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${transaction.transaction_id}</td>
                    <td>${transaction.customer_name}</td>
                    <td>${transaction.type}</td>
                    <td>₹${formatNumber(transaction.amount)}</td>
                    <td>${transaction.gold_grams.toFixed(3)}g</td>
                    <td><span class="status ${transaction.status.toLowerCase()}">${transaction.status}</span></td>
                    <td>${transaction.payment_method}</td>
                    <td>${formatDate(transaction.timestamp)}</td>
                `;
            });

            document.getElementById('transactionsLoading').style.display = 'none';
            document.getElementById('transactionsContent').style.display = 'block';
        }

        // Tab functionality
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Remove active class from all nav tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName + 'Tab').classList.add('active');
            event.target.classList.add('active');
            
            // Load tab-specific data
            loadTabData(tabName);
        }

        // Load tab-specific data
        async function loadTabData(tabName) {
            switch(tabName) {
                case 'customers':
                    await loadCustomers();
                    break;
                case 'transactions':
                    await loadTransactions();
                    break;
                case 'schemes':
                    await loadSchemes();
                    break;
                case 'analytics':
                    await loadAnalytics();
                    break;
            }
        }

        // Load customers
        async function loadCustomers() {
            try {
                const response = await makeApiRequest('/admin/customers?limit=50');
                if (response.success && response.data) {
                    updateCustomersTable(response.data.customers);
                }
            } catch (error) {
                showError('Failed to load customers: ' + error.message);
            }
        }

        // Load transactions
        async function loadTransactions() {
            try {
                const response = await makeApiRequest('/admin/transactions?limit=50');
                if (response.success && response.data) {
                    updateTransactionsTable(response.data.transactions);
                }
            } catch (error) {
                showError('Failed to load transactions: ' + error.message);
            }
        }

        // Load schemes
        async function loadSchemes() {
            try {
                const response = await makeApiRequest('/admin/schemes?limit=50');
                if (response.success && response.data) {
                    updateSchemesTable(response.data.schemes);
                }
            } catch (error) {
                showError('Failed to load schemes: ' + error.message);
            }
        }

        // Update schemes table
        function updateSchemesTable(schemes) {
            const tbody = document.getElementById('schemesTableBody');
            tbody.innerHTML = '';

            schemes.forEach(scheme => {
                const progress = `${scheme.paid_months}/${scheme.duration_months}`;
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${scheme.scheme_id}</td>
                    <td>${scheme.customer_name}</td>
                    <td>${scheme.scheme_type}</td>
                    <td>₹${formatNumber(scheme.monthly_amount)}</td>
                    <td>${scheme.duration_months} months</td>
                    <td>${progress}</td>
                    <td>${scheme.gold_accumulated.toFixed(3)}g</td>
                    <td><span class="status ${scheme.status}">${scheme.status}</span></td>
                `;
            });

            document.getElementById('schemesLoading').style.display = 'none';
            document.getElementById('schemesContent').style.display = 'block';
        }

        // Load analytics
        async function loadAnalytics() {
            // Calculate analytics from dashboard data
            const stats = await makeApiRequest('/admin/dashboard');
            if (stats.success && stats.data) {
                const data = stats.data.stats;
                
                // Calculate metrics
                const avgTransaction = data.total_transactions > 0 ? 
                    (data.total_revenue / data.total_transactions) : 0;
                const successRate = data.total_transactions > 0 ? 
                    ((data.total_transactions - (data.failed_transactions || 0)) / data.total_transactions * 100) : 0;
                
                document.getElementById('avgTransaction').textContent = `₹${formatNumber(avgTransaction)}`;
                document.getElementById('successRate').textContent = `${successRate.toFixed(1)}%`;
                document.getElementById('monthlyGrowth').textContent = '+12.5%'; // Placeholder
                document.getElementById('customerRetention').textContent = '85%'; // Placeholder
            }
        }

        // Utility functions
        function formatNumber(num) {
            return new Intl.NumberFormat('en-IN').format(num);
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-IN');
        }

        function showError(message) {
            console.error(message);
            // You can implement a toast notification here
        }

        // Refresh dashboard
        async function refreshDashboard() {
            await loadDashboardData();
        }

        // Auto-refresh functionality
        function startAutoRefresh() {
            refreshInterval = setInterval(async () => {
                await loadDashboardData();
            }, 30000); // Refresh every 30 seconds
        }

        // Logout functionality
        async function logout() {
            try {
                if (authToken) {
                    await makeApiRequest('/auth/logout', { method: 'POST' });
                }
            } catch (error) {
                console.error('Logout error:', error);
            } finally {
                authToken = null;
                if (refreshInterval) {
                    clearInterval(refreshInterval);
                }
                document.getElementById('dashboard').style.display = 'none';
                document.getElementById('loginScreen').style.display = 'block';
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 VMUrugan Admin Portal - Backend API Version');
            checkBackendConnection();
        });
    </script>
</body>
</html>
