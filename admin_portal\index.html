<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VMUrugan Admin Portal</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            min-height: 100vh;
        }
        
        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .login-card {
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: #FFD700;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: white;
            font-weight: bold;
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 24px;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }
        
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        input:focus {
            outline: none;
            border-color: #FFD700;
        }
        
        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #FFD700 0%, #2E7D32 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }

        .login-btn:hover {
            background: linear-gradient(135deg, #2E7D32 0%, #FFD700 100%);
        }
        
        .demo-creds {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #2196f3;
        }
        
        .demo-creds h4 {
            color: #1976d2;
            margin-bottom: 8px;
        }
        
        .demo-creds p {
            color: #666;
            font-size: 14px;
            margin: 0;
        }
        
        .dashboard {
            display: none;
            min-height: 100vh;
            background: #f5f5f5;
        }
        
        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, #1B5E20 0%, #2E7D32 100%);
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            padding: 20px 0;
        }
        
        .sidebar-header {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        
        .sidebar-header h2 {
            color: white;
            margin-top: 10px;
        }
        
        .nav-item {
            padding: 15px 20px;
            color: white;
            cursor: pointer;
            transition: background 0.3s;
            border-left: 4px solid transparent;
        }
        
        .nav-item:hover, .nav-item.active {
            background: rgba(255,255,255,0.2);
            border-left-color: white;
        }
        
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        
        .top-bar {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: bold;
            color: #2E7D32;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .content-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th, .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .status-verified {
            background: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }

        .btn-small {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .btn-small:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div id="loginScreen" class="login-container">
        <div class="login-card">
            <div class="logo">VM</div>
            <h1>VMUrugan Admin Portal</h1>
            <p class="subtitle">Business Management Dashboard</p>
            
            <form id="loginForm">
                <div class="form-group">
                    <label for="username">Admin Username</label>
                    <input type="text" id="username" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Admin Password</label>
                    <input type="password" id="password" required>
                </div>
                
                <button type="submit" class="login-btn">Login to Admin Portal</button>
            </form>
            
            <div class="demo-creds">
                <h4>🔑 Demo Credentials</h4>
                <p>Username: <strong>admin</strong></p>
                <p>Password: <strong>VMURUGAN_ADMIN_2025</strong></p>
            </div>
            
            <p style="color: #666; font-size: 12px; margin-top: 20px;">
                🔒 Secure admin access only
            </p>
        </div>
    </div>

    <!-- Dashboard -->
    <div id="dashboard" class="dashboard">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo" style="width: 60px; height: 60px; font-size: 24px;">VM</div>
                <h2>VMUrugan</h2>
                <p style="color: rgba(255,255,255,0.8); font-size: 14px;">Admin Portal</p>
            </div>
            
            <div class="nav-item active" onclick="showSection('overview')">📊 Dashboard</div>
            <div class="nav-item" onclick="showSection('customers')">👥 Customers</div>
            <div class="nav-item" onclick="showSection('transactions')">💳 Transactions</div>
            <div class="nav-item" onclick="showSection('analytics')">📈 Analytics</div>
            <div class="nav-item" onclick="showSection('settings')">⚙️ Settings</div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Bar -->
            <div class="top-bar">
                <h1 id="pageTitle">Dashboard Overview</h1>
                <div>
                    <button onclick="refreshData()" style="margin-right: 10px; padding: 8px 16px; border: none; background: #FFD700; border-radius: 4px; cursor: pointer;">🔄 Refresh</button>
                    <button onclick="debugFirebase()" style="margin-right: 10px; padding: 8px 16px; border: none; background: #FF5722; color: white; border-radius: 4px; cursor: pointer;">🔍 Debug Firebase</button>
                    <button onclick="logout()" class="logout-btn">Logout</button>
                </div>
            </div>

            <!-- Dashboard Overview -->
            <div id="overviewSection">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="dashboardCustomers">0</div>
                        <div class="stat-label">Total Customers</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="dashboardTransactions">0</div>
                        <div class="stat-label">Total Transactions</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="dashboardRevenue">₹0</div>
                        <div class="stat-label">Total Revenue</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="dashboardGoldSold">0g</div>
                        <div class="stat-label">Gold Sold</div>
                    </div>
                </div>

                <div class="content-card">
                    <h3>Recent Activity</h3>
                    <ul style="list-style: none; padding: 0;">
                        <li style="padding: 10px 0; border-bottom: 1px solid #eee;">
                            <strong>New customer registered</strong> - 2 minutes ago
                        </li>
                        <li style="padding: 10px 0; border-bottom: 1px solid #eee;">
                            <strong>Gold purchase: ₹5,000</strong> - 5 minutes ago
                        </li>
                        <li style="padding: 10px 0; border-bottom: 1px solid #eee;">
                            <strong>Payment completed</strong> - 10 minutes ago
                        </li>
                        <li style="padding: 10px 0;">
                            <strong>Customer KYC verified</strong> - 15 minutes ago
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Customers Section -->
            <div id="customersSection" style="display: none;">
                <div class="content-card">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h3>Customer Management</h3>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <label style="font-size: 14px; color: #666;">
                                <input type="checkbox" id="showTestData" onchange="toggleTestData()" style="margin-right: 5px;">
                                Show Test/Mock Data
                            </label>
                            <button onclick="refreshCustomers()" style="padding: 8px 16px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                🔄 Refresh
                            </button>
                        </div>
                    </div>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Customer ID</th>
                                <th>Name</th>
                                <th>Phone</th>
                                <th>Email</th>
                                <th>Schemes</th>
                                <th>Invested</th>
                                <th>Gold Holdings</th>
                                <th>KYC Status</th>
                            </tr>
                        </thead>
                        <tbody id="customersTableBody">
                            <tr>
                                <td colspan="8" style="text-align: center; padding: 20px;">
                                    <div style="color: #666;">
                                        📊 Loading customer data from Firebase...<br>
                                        <small>Real customer registrations will appear here</small>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Transactions Section -->
            <div id="transactionsSection" style="display: none;">
                <div class="content-card">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h3>Transaction History</h3>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <input type="text" id="transactionSearch" placeholder="Search transactions..." style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 200px;">
                            <select id="statusFilter" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                <option value="">All Status</option>
                                <option value="SUCCESS">Success</option>
                                <option value="PENDING">Pending</option>
                                <option value="FAILED">Failed</option>
                            </select>
                            <button onclick="refreshTransactions()" style="padding: 8px 16px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                🔄 Refresh
                            </button>
                        </div>
                    </div>

                    <!-- Transaction Stats -->
                    <div class="stats-grid" style="margin-bottom: 20px;">
                        <div class="stat-card">
                            <div class="stat-value" id="totalTransactions">0</div>
                            <div class="stat-label">Total Transactions</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="totalRevenue">₹0</div>
                            <div class="stat-label">Total Revenue</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="totalGoldSold">0g</div>
                            <div class="stat-label">Gold Sold</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="successRate">0%</div>
                            <div class="stat-label">Success Rate</div>
                        </div>
                    </div>

                    <table class="table">
                        <thead>
                            <tr>
                                <th>Transaction ID</th>
                                <th>Customer ID</th>
                                <th>Scheme ID</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Gold</th>
                                <th>Payment Method</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="transactionsTableBody">
                            <tr>
                                <td colspan="10" style="text-align: center; padding: 20px;">
                                    <div style="color: #666;">
                                        💳 Loading transaction data from Firebase...<br>
                                        <small>Real transactions will appear here</small>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Analytics Section -->
            <div id="analyticsSection" style="display: none;">
                <div class="content-card">
                    <h3>Business Analytics</h3>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value">12.5%</div>
                            <div class="stat-label">Conversion Rate</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">₹4,567</div>
                            <div class="stat-label">Avg. Order Value</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">85%</div>
                            <div class="stat-label">Customer Retention</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">98.2%</div>
                            <div class="stat-label">Payment Success</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Section -->
            <div id="settingsSection" style="display: none;">
                <div class="content-card">
                    <h3>System Settings</h3>
                    <p><strong>Firebase Status:</strong> <span style="color: green;">✅ Connected</span></p>
                    <p><strong>Project ID:</strong> vmurugan-gold-trading</p>
                    <p><strong>Business ID:</strong> VMURUGAN_001</p>
                    <p><strong>Admin Token:</strong> VMURUGAN_ADMIN_2025</p>
                    <br>
                    <button onclick="testFirebase()" style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">Test Firebase Connection</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Login functionality
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (username === 'admin' && password === 'VMURUGAN_ADMIN_2025') {
                document.getElementById('loginScreen').style.display = 'none';
                document.getElementById('dashboard').style.display = 'block';
            } else {
                alert('❌ Invalid credentials. Please try again.');
            }
        });

        // Navigation
        function showSection(section) {
            // Hide all sections
            document.querySelectorAll('[id$="Section"]').forEach(el => {
                el.style.display = 'none';
            });

            // Remove active class from nav items
            document.querySelectorAll('.nav-item').forEach(el => {
                el.classList.remove('active');
            });

            // Show selected section
            document.getElementById(section + 'Section').style.display = 'block';

            // Add active class to clicked nav item
            event.target.classList.add('active');

            // Update page title
            const titles = {
                'overview': 'Dashboard Overview',
                'customers': 'Customer Management',
                'transactions': 'Transaction History',
                'analytics': 'Business Analytics',
                'settings': 'System Settings'
            };
            document.getElementById('pageTitle').textContent = titles[section];

            // Load data for specific sections
            if (section === 'transactions') {
                loadTransactionsFromFirebase();
            } else if (section === 'customers') {
                loadCustomersFromFirebase();
            }
        }

        // Utility functions
        function refreshData() {
            alert('🔄 Data refreshed successfully!');
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                document.getElementById('dashboard').style.display = 'none';
                document.getElementById('loginScreen').style.display = 'flex';
                document.getElementById('username').value = '';
                document.getElementById('password').value = '';
            }
        }

        function testFirebase() {
            console.log('🔧 Testing Firebase connection...');

            // Show loading message
            const tbody = document.getElementById('customersTableBody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" style="text-align: center; padding: 20px;">
                        <div style="color: #2E7D32;">
                            🔄 Testing Firebase connection...<br>
                            <small>Check browser console for detailed logs</small>
                        </div>
                    </td>
                </tr>
            `;

            // Test connection
            loadCustomersFromFirebase();

            // Show test results
            setTimeout(() => {
                alert('🔧 Firebase Connection Test\n\n' +
                      '📊 Project: vmurugan-gold-trading\n' +
                      '🔗 Status: Testing multiple endpoints\n' +
                      '📝 Collections: customers, transactions\n' +
                      '🔍 Check browser console (F12) for detailed logs\n\n' +
                      '💡 If no customers appear:\n' +
                      '1. Register in mobile app first\n' +
                      '2. Check browser console for errors\n' +
                      '3. Refresh this page');
            }, 2000);
        }

        // Load real customer data from Firebase
        async function loadCustomersFromFirebase() {
            try {
                const apiKey = 'AIzaSyCaS4pdX3a_JFdL0PolTHYnpebg5ppbgs0';

                // Try multiple Firebase endpoints
                const endpoints = [
                    'https://firestore.googleapis.com/v1/projects/vmurugan-gold-trading/databases/(default)/documents/customers',
                    'https://vmurugan-gold-trading-default-rtdb.firebaseio.com/customers.json',
                    'https://vmurugan-gold-trading-default-rtdb.asia-southeast1.firebaseio.com/customers.json'
                ];

                for (const endpoint of endpoints) {
                    try {
                        // Add API key to the URL
                        const url = endpoint.includes('firebaseio.com')
                            ? `${endpoint}?auth=${apiKey}`
                            : `${endpoint}?key=${apiKey}`;

                        console.log('Trying endpoint:', url);
                        const response = await fetch(url);

                        if (response.ok) {
                            const data = await response.json();
                            console.log('Firebase response:', data);

                            if (data && (data.documents || Object.keys(data).length > 0)) {
                                displayCustomers(data.documents || Object.values(data));
                                return;
                            }
                        }
                    } catch (e) {
                        console.log('Endpoint failed:', endpoint, e);
                    }
                }

                // If all endpoints fail, show demo data with instructions
                displayNoCustomersMessage();

            } catch (error) {
                console.log('Firebase connection error:', error);
                displayNoCustomersMessage();
            }
        }

        function displayCustomers(customers) {
            const tbody = document.getElementById('customersTableBody');

            // Store data for filter toggle
            window.lastCustomersData = customers;

            if (!customers || customers.length === 0) {
                displayNoCustomersMessage();
                return;
            }

            // Filter out test/mock data - only show real user registrations
            const realCustomers = customers.filter(customer => {
                let dataType = 'unknown';
                let phone = '';
                let name = '';
                let email = '';

                if (customer.fields) {
                    // Firestore format
                    dataType = customer.fields.data_type?.stringValue || 'unknown';
                    phone = customer.fields.phone?.stringValue || '';
                    name = customer.fields.name?.stringValue || '';
                    email = customer.fields.email?.stringValue || '';
                } else {
                    // Realtime Database format
                    dataType = customer.data_type || 'unknown';
                    phone = customer.phone || '';
                    name = customer.name || '';
                    email = customer.email || '';
                }

                // Comprehensive filter for test/mock data
                const isTestData =
                    // Phone number patterns
                    phone.includes('test') ||
                    phone.includes('Test') ||
                    phone.includes('TEST_') ||
                    phone.includes('9999999999') ||
                    phone.includes('1234567890') ||
                    phone.includes('0000000000') ||
                    phone === '9876543210' ||  // Common test number
                    phone === '8765432109' ||  // Common test number

                    // Name patterns
                    name.includes('Test') ||
                    name.includes('test') ||
                    name.includes('Demo') ||
                    name.includes('Sample') ||
                    name.includes('Mock') ||
                    name === 'John Doe' ||
                    name === 'Jane Smith' ||

                    // Email patterns
                    email.includes('test@') ||
                    email.includes('demo@') ||
                    email.includes('sample@') ||
                    email.includes('@test.') ||
                    email.includes('@example.') ||

                    // Data type markers
                    dataType === 'test' ||
                    dataType === 'demo' ||
                    dataType === 'mock';

                // Apply filter based on toggle
                if (showTestDataFlag) {
                    // Show all data when toggle is on
                    return true;
                } else {
                    // Only show data that is explicitly marked as real user data
                    // OR if it doesn't match any test patterns (for backward compatibility)
                    return !isTestData && (dataType === 'real_user' || dataType === 'unknown');
                }
            });

            if (realCustomers.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 40px; color: #666;">
                            <div style="font-size: 18px; margin-bottom: 10px;">📱 No Real Customer Registrations Yet</div>
                            <div style="font-size: 14px;">
                                Real customer data will appear here when users register through the mobile app.<br>
                                Test data and mock entries are filtered out.
                            </div>
                        </td>
                    </tr>
                `;
                updateCustomerStats(0);
                return;
            }

            tbody.innerHTML = realCustomers.map(customer => {
                // Handle different Firebase data structures
                let customerId, name, phone, email, registrationDate;

                if (customer.fields) {
                    // Firestore format
                    customerId = customer.fields.customer_id?.stringValue || 'N/A';
                    name = customer.fields.name?.stringValue || customer.fields.fullName?.stringValue || 'N/A';
                    phone = customer.fields.phone?.stringValue || customer.fields.phoneNumber?.stringValue || 'N/A';
                    email = customer.fields.email?.stringValue || 'N/A';
                    registrationDate = customer.fields.registration_date?.stringValue || customer.fields.createdAt?.stringValue || 'Recent';
                } else {
                    // Realtime Database format
                    customerId = customer.customer_id || 'N/A';
                    name = customer.name || customer.fullName || 'N/A';
                    phone = customer.phone || customer.phoneNumber || 'N/A';
                    email = customer.email || 'N/A';
                    registrationDate = customer.registration_date || customer.createdAt || 'Recent';
                }

                return `
                    <tr>
                        <td><strong style="color: #2E7D32;">${customerId}</strong></td>
                        <td><strong>${name}</strong></td>
                        <td>${phone}</td>
                        <td>${email}</td>
                        <td><button onclick="loadCustomerSchemes('${customerId}')" class="btn-small">View Schemes</button></td>
                        <td>₹0 <small>(Loading...)</small></td>
                        <td>0g</td>
                        <td><span class="status-badge status-pending">Active</span></td>
                    </tr>
                `;
            }).join('');

            // Update stats with real customer count
            updateCustomerStats(realCustomers.length);
        }

        function displayNoCustomersMessage() {
            const tbody = document.getElementById('customersTableBody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" style="text-align: center; padding: 30px;">
                        <div style="color: #666; line-height: 1.6;">
                            <h3 style="color: #2E7D32; margin-bottom: 15px;">🔍 Looking for Customer Data...</h3>

                            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 15px 0;">
                                <strong>📱 To see your registration here:</strong><br>
                                1. Open VMUrugan mobile app<br>
                                2. Complete customer registration<br>
                                3. Refresh this admin portal<br>
                            </div>

                            <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; border-left: 4px solid #2E7D32;">
                                <strong>✅ Firebase Status:</strong> Connected<br>
                                <strong>📊 Data Source:</strong> Real-time from mobile app<br>
                                <strong>🔄 Auto-refresh:</strong> Every 30 seconds
                            </div>

                            <button onclick="loadCustomersFromFirebase()" style="margin-top: 15px; padding: 10px 20px; background: #2E7D32; color: white; border: none; border-radius: 5px; cursor: pointer;">
                                🔄 Refresh Customer Data
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }

        function updateCustomerStats(customerCount) {
            // Update the main dashboard customer count
            const dashboardCustomersEl = document.getElementById('dashboardCustomers');
            if (dashboardCustomersEl) {
                dashboardCustomersEl.textContent = customerCount.toString();
            }
        }

        // Auto-refresh functionality
        let refreshInterval;

        function startAutoRefresh() {
            // Refresh every 30 seconds
            refreshInterval = setInterval(loadCustomersFromFirebase, 30000);
        }

        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        }

        // Enhanced refresh function
        function refreshData() {
            console.log('🔄 Manual refresh triggered');
            loadCustomersFromFirebase();
        }

        function refreshCustomers() {
            console.log('🔄 Refreshing customer data...');
            loadCustomersFromFirebase();
        }

        let showTestDataFlag = false;

        function toggleTestData() {
            showTestDataFlag = document.getElementById('showTestData').checked;
            console.log('🔄 Test data filter toggled:', showTestDataFlag ? 'SHOW' : 'HIDE');

            // Re-display customers with new filter
            if (window.lastCustomersData) {
                displayCustomers(window.lastCustomersData);
            }
        }

        // Load real transaction data from Firebase
        async function loadTransactionsFromFirebase() {
            try {
                const apiKey = 'AIzaSyCaS4pdX3a_JFdL0PolTHYnpebg5ppbgs0';

                // Try Firebase Firestore endpoint for transactions
                const endpoint = `https://firestore.googleapis.com/v1/projects/vmurugan-gold-trading/databases/(default)/documents/transactions?key=${apiKey}`;

                console.log('🔄 Loading transactions from Firebase...');
                console.log('🌐 Endpoint:', endpoint);

                const response = await fetch(endpoint);
                console.log('📥 Response status:', response.status);

                if (response.ok) {
                    const data = await response.json();
                    console.log('📊 Firebase transactions response:', data);

                    if (data && data.documents && data.documents.length > 0) {
                        console.log(`✅ Found ${data.documents.length} transactions`);
                        displayTransactions(data.documents);
                        return;
                    } else {
                        console.log('📭 No transactions found in Firebase');
                        console.log('💡 This means either:');
                        console.log('   1. No gold purchases have been made yet');
                        console.log('   2. Transactions are not being saved to Firebase');
                        console.log('   3. Transactions are in a different collection');
                    }
                } else {
                    const errorText = await response.text();
                    console.log('❌ Firebase response error:', response.status, errorText);
                }

                // If no real transactions, show demo data with instructions
                displayNoTransactionsMessage();

            } catch (error) {
                console.log('❌ Firebase transaction loading error:', error);
                displayNoTransactionsMessage();
            }
        }

        function displayTransactions(transactions) {
            const tbody = document.getElementById('transactionsTableBody');

            if (!transactions || transactions.length === 0) {
                displayNoTransactionsMessage();
                return;
            }

            // Filter and process transactions
            const processedTransactions = transactions.map(transaction => {
                const fields = transaction.fields;
                return {
                    transaction_id: fields.transaction_id?.stringValue || 'N/A',
                    customer_name: fields.customer_name?.stringValue || 'N/A',
                    customer_phone: fields.customer_phone?.stringValue || 'N/A',
                    type: fields.type?.stringValue || 'BUY',
                    amount: parseFloat(fields.amount?.doubleValue || 0),
                    gold_grams: parseFloat(fields.gold_grams?.doubleValue || 0),
                    gold_price_per_gram: parseFloat(fields.gold_price_per_gram?.doubleValue || 0),
                    payment_method: fields.payment_method?.stringValue || 'N/A',
                    status: fields.status?.stringValue || 'PENDING',
                    gateway_transaction_id: fields.gateway_transaction_id?.stringValue || 'N/A',
                    timestamp: fields.timestamp?.stringValue || new Date().toISOString(),
                    device_info: fields.device_info?.stringValue || 'N/A',
                    location: fields.location?.stringValue || 'N/A'
                };
            });

            // Sort by timestamp (newest first)
            processedTransactions.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

            // Apply search and status filters
            const searchTerm = document.getElementById('transactionSearch')?.value.toLowerCase() || '';
            const statusFilter = document.getElementById('statusFilter')?.value || '';

            const filteredTransactions = processedTransactions.filter(transaction => {
                const matchesSearch = !searchTerm ||
                    transaction.transaction_id.toLowerCase().includes(searchTerm) ||
                    transaction.customer_name.toLowerCase().includes(searchTerm) ||
                    transaction.customer_phone.includes(searchTerm);

                const matchesStatus = !statusFilter || transaction.status === statusFilter;

                return matchesSearch && matchesStatus;
            });

            if (filteredTransactions.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="10" style="text-align: center; padding: 40px; color: #666;">
                            <div style="font-size: 18px; margin-bottom: 10px;">🔍 No Transactions Found</div>
                            <div style="font-size: 14px;">
                                ${searchTerm || statusFilter ? 'Try adjusting your search or filter criteria.' : 'Real transactions will appear here when customers make purchases.'}
                            </div>
                        </td>
                    </tr>
                `;
                updateTransactionStats([], processedTransactions);
                return;
            }

            tbody.innerHTML = filteredTransactions.map(transaction => {
                const statusClass = getStatusClass(transaction.status);
                const formattedDate = formatTransactionDate(transaction.timestamp);

                return `
                    <tr>
                        <td><strong>${transaction.transaction_id}</strong></td>
                        <td>
                            <div><strong style="color: #2E7D32;">${transaction.customer_id || 'N/A'}</strong></div>
                            <div style="font-size: 12px; color: #666;">${transaction.customer_name} (${transaction.customer_phone})</div>
                        </td>
                        <td>
                            <div><strong style="color: #FF9800;">${transaction.scheme_id || 'No Scheme'}</strong></div>
                        </td>
                        <td><span style="background: #e3f2fd; color: #1976d2; padding: 2px 8px; border-radius: 12px; font-size: 12px;">${transaction.type}</span></td>
                        <td><strong>₹${transaction.amount.toLocaleString()}</strong></td>
                        <td>${transaction.gold_grams.toFixed(3)}g</td>
                        <td>${transaction.payment_method}</td>
                        <td><span class="status-badge ${statusClass}">${transaction.status}</span></td>
                        <td>${formattedDate}</td>
                        <td>
                            <button onclick="viewTransactionDetails('${transaction.transaction_id}')" style="padding: 4px 8px; background: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                                View
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');

            // Update transaction statistics
            updateTransactionStats(filteredTransactions, processedTransactions);
        }

        function displayNoTransactionsMessage() {
            const tbody = document.getElementById('transactionsTableBody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="10" style="text-align: center; padding: 30px;">
                        <div style="color: #666; line-height: 1.6;">
                            <h3 style="color: #FF9800; margin-bottom: 15px;">💳 Looking for Transaction Data...</h3>

                            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 15px 0;">
                                <strong>📱 To see transactions here:</strong><br>
                                1. Open VMUrugan mobile app<br>
                                2. Complete a gold purchase<br>
                                3. Refresh this admin portal<br>
                            </div>

                            <div style="background: #fff3e0; padding: 15px; border-radius: 8px; border-left: 4px solid #FF9800;">
                                <strong>✅ Firebase Status:</strong> Connected<br>
                                <strong>📊 Data Source:</strong> Real-time from mobile app<br>
                                <strong>🔄 Auto-refresh:</strong> Every 30 seconds
                            </div>

                            <button onclick="loadTransactionsFromFirebase()" style="margin-top: 15px; padding: 10px 20px; background: #FF9800; color: white; border: none; border-radius: 5px; cursor: pointer;">
                                🔄 Refresh Transaction Data
                            </button>
                        </div>
                    </td>
                </tr>
            `;

            // Reset stats
            updateTransactionStats([], []);
        }

        function updateTransactionStats(filteredTransactions, allTransactions) {
            const totalTransactions = allTransactions.length;
            const successfulTransactions = allTransactions.filter(t => t.status === 'SUCCESS');
            const totalRevenue = successfulTransactions.reduce((sum, t) => sum + t.amount, 0);
            const totalGoldSold = successfulTransactions.reduce((sum, t) => sum + t.gold_grams, 0);
            const successRate = totalTransactions > 0 ? (successfulTransactions.length / totalTransactions * 100) : 0;

            // Update transaction page stats
            const totalTransactionsEl = document.getElementById('totalTransactions');
            const totalRevenueEl = document.getElementById('totalRevenue');
            const totalGoldSoldEl = document.getElementById('totalGoldSold');
            const successRateEl = document.getElementById('successRate');

            if (totalTransactionsEl) totalTransactionsEl.textContent = totalTransactions.toString();
            if (totalRevenueEl) totalRevenueEl.textContent = `₹${totalRevenue.toLocaleString()}`;
            if (totalGoldSoldEl) totalGoldSoldEl.textContent = `${totalGoldSold.toFixed(3)}g`;
            if (successRateEl) successRateEl.textContent = `${successRate.toFixed(1)}%`;

            // Update main dashboard stats
            const dashboardTransactionsEl = document.getElementById('dashboardTransactions');
            const dashboardRevenueEl = document.getElementById('dashboardRevenue');
            const dashboardGoldSoldEl = document.getElementById('dashboardGoldSold');

            if (dashboardTransactionsEl) dashboardTransactionsEl.textContent = totalTransactions.toString();
            if (dashboardRevenueEl) dashboardRevenueEl.textContent = `₹${totalRevenue.toLocaleString()}`;
            if (dashboardGoldSoldEl) dashboardGoldSoldEl.textContent = `${totalGoldSold.toFixed(3)}g`;

            // Store data globally for other functions
            window.transactionStats = {
                totalTransactions,
                totalRevenue,
                totalGoldSold,
                successRate
            };
        }

        function getStatusClass(status) {
            switch (status) {
                case 'SUCCESS': return 'status-verified';
                case 'PENDING': return 'status-pending';
                case 'FAILED': return 'status-rejected';
                default: return 'status-pending';
            }
        }

        function formatTransactionDate(timestamp) {
            try {
                const date = new Date(timestamp);
                return date.toLocaleDateString('en-IN') + ' ' + date.toLocaleTimeString('en-IN', { hour: '2-digit', minute: '2-digit' });
            } catch (e) {
                return timestamp;
            }
        }

        function viewTransactionDetails(transactionId) {
            alert(`Transaction Details for ${transactionId}\n\nDetailed view coming soon...`);
        }

        function refreshTransactions() {
            console.log('🔄 Refreshing transaction data...');
            loadTransactionsFromFirebase();
        }

        // Add search and filter event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Search functionality
            const searchInput = document.getElementById('transactionSearch');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    if (window.lastTransactionData) {
                        displayTransactions(window.lastTransactionData);
                    }
                });
            }

            // Status filter functionality
            const statusFilter = document.getElementById('statusFilter');
            if (statusFilter) {
                statusFilter.addEventListener('change', function() {
                    if (window.lastTransactionData) {
                        displayTransactions(window.lastTransactionData);
                    }
                });
            }
        });

        // Load customer schemes
        async function loadCustomerSchemes(customerId) {
            try {
                console.log(`🔍 Loading schemes for customer: ${customerId}`);

                // Try multiple Firebase endpoints for schemes
                const endpoints = [
                    `https://firestore.googleapis.com/v1/projects/vmurugan-gold-trading/databases/(default)/documents/schemes?key=AIzaSyCaS4pdX3a_JFdL0PolTHYnpebg5ppbgs0`,
                ];

                for (const endpoint of endpoints) {
                    try {
                        const response = await fetch(endpoint);
                        if (response.ok) {
                            const data = await response.json();
                            console.log('Schemes response:', data);

                            if (data && data.documents) {
                                // Filter schemes for this customer
                                const customerSchemes = data.documents.filter(scheme => {
                                    const schemeCustomerId = scheme.fields?.customer_id?.stringValue;
                                    return schemeCustomerId === customerId;
                                });

                                if (customerSchemes.length > 0) {
                                    displayCustomerSchemes(customerId, customerSchemes);
                                    return;
                                }
                            }
                        }
                    } catch (e) {
                        console.log('Schemes endpoint failed:', endpoint, e);
                    }
                }

                alert(`No schemes found for customer ${customerId}`);

            } catch (error) {
                console.error('Error loading customer schemes:', error);
                alert('Error loading schemes. Check console for details.');
            }
        }

        // Display customer schemes in a modal or alert
        function displayCustomerSchemes(customerId, schemes) {
            const schemeList = schemes.map(scheme => {
                const fields = scheme.fields;
                const schemeId = fields.scheme_id?.stringValue || 'N/A';
                const monthlyAmount = fields.monthly_amount?.doubleValue || 0;
                const status = fields.status?.stringValue || 'N/A';
                const schemeType = fields.scheme_type?.stringValue || 'N/A';
                const createdDate = fields.created_date?.stringValue || 'N/A';

                return `
                    🆔 Scheme ID: ${schemeId}
                    💰 Monthly Amount: ₹${monthlyAmount}
                    📅 Type: ${schemeType}
                    ✅ Status: ${status}
                    📆 Created: ${new Date(createdDate).toLocaleDateString()}
                `;
            }).join('\n\n');

            alert(`Schemes for Customer ${customerId}:\n\n${schemeList}`);
        }

        // Debug Firebase collections
        async function debugFirebase() {
            console.log('🔍 DEBUGGING FIREBASE COLLECTIONS...');

            const apiKey = 'AIzaSyCaS4pdX3a_JFdL0PolTHYnpebg5ppbgs0';
            const baseUrl = 'https://firestore.googleapis.com/v1/projects/vmurugan-gold-trading/databases/(default)/documents';

            const collections = ['customers', 'transactions', 'schemes', 'counters'];

            for (const collection of collections) {
                try {
                    console.log(`\n📂 Checking collection: ${collection}`);
                    const endpoint = `${baseUrl}/${collection}?key=${apiKey}`;

                    const response = await fetch(endpoint);
                    console.log(`   Status: ${response.status}`);

                    if (response.ok) {
                        const data = await response.json();
                        if (data.documents && data.documents.length > 0) {
                            console.log(`   ✅ Found ${data.documents.length} documents`);
                            console.log(`   📄 Sample document:`, data.documents[0]);
                        } else {
                            console.log(`   📭 No documents found`);
                        }
                    } else {
                        const errorText = await response.text();
                        console.log(`   ❌ Error: ${errorText}`);
                    }
                } catch (error) {
                    console.log(`   ❌ Exception: ${error}`);
                }
            }

            console.log('\n🎯 SUMMARY:');
            console.log('Check the console above for detailed results');
            console.log('If transactions collection is empty, transactions are not being saved');
            alert('Debug complete! Check browser console (F12) for detailed results.');
        }

        // Load customers and transactions when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Admin portal loaded, connecting to Firebase...');
            setTimeout(() => {
                loadCustomersFromFirebase();
                loadTransactionsFromFirebase();
            }, 1000);
            startAutoRefresh();
        });

        // Enhanced auto-refresh to include transactions
        function startAutoRefresh() {
            // Refresh every 30 seconds
            refreshInterval = setInterval(() => {
                loadCustomersFromFirebase();
                loadTransactionsFromFirebase();
            }, 30000);
        }

        // Enhanced refresh function
        function refreshData() {
            console.log('🔄 Manual refresh triggered');
            loadCustomersFromFirebase();
            loadTransactionsFromFirebase();
        }

        // Stop auto-refresh when user leaves page
        window.addEventListener('beforeunload', stopAutoRefresh);
    </script>
</body>
</html>
