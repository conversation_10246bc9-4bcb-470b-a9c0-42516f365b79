{"name": "vmurugan-gold-trading-api", "version": "2.0.0", "description": "VMUrugan Gold Trading Platform - On-Premises Backend API with SQL Server", "main": "server-sqlserver.js", "scripts": {"start": "node server-sqlserver.js", "dev": "nodemon server-sqlserver.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "setup": "node setup-sqlserver.js", "local-setup": "node local-setup.js", "local-start": "cp .env.local .env && npm run setup && npm run dev", "docs": "node -e \"console.log('API Documentation: http://localhost:3000/api-docs')\"", "health": "curl http://localhost:3000/health || echo 'Server not running'", "admin-login": "curl -X POST http://localhost:3000/api/auth/login -H 'Content-Type: application/json' -d '{\"phone\":\"**********\",\"password\":\"VMURUGAN_ADMIN_2025\"}'", "test-customer": "curl -X POST http://localhost:3000/api/auth/login -H 'Content-Type: application/json' -d '{\"phone\":\"**********\",\"password\":\"test123\"}'", "test-local": "node test-local.js", "test-all": "npm test && npm run test-local", "test-comprehensive": "node run-all-tests.js", "clean": "rm -rf node_modules package-lock.json && npm install", "logs": "tail -f logs/api.log"}, "dependencies": {"axios": "^1.11.0", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "mssql": "^10.0.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.1"}, "devDependencies": {"colors": "^1.4.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "keywords": ["gold", "trading", "business", "api", "sqlserver", "jwt", "authentication", "vmurugan"], "author": "VMUrugan Gold Trading", "license": "MIT", "engines": {"node": ">=16.0.0"}, "jest": {"testEnvironment": "node", "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"], "testMatch": ["<rootDir>/tests/**/*.test.js"], "collectCoverageFrom": ["*.js", "routes/*.js", "!node_modules/**", "!tests/**", "!coverage/**"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "testTimeout": 30000, "verbose": true, "detectOpenHandles": true, "forceExit": true}}