# VMUrugan Gold Trading API - Environment Configuration
# Copy this file to .env and update with your actual values

# Server Configuration
PORT=3000
NODE_ENV=production
API_VERSION=v1

# SQL Server Database Configuration
DB_SERVER=localhost
DB_PORT=1433
DB_NAME=VMUruganGoldTrading
DB_USER=vmurugan_api_user
DB_PASSWORD=your_secure_password_here
DB_ENCRYPT=true
DB_TRUST_SERVER_CERTIFICATE=false

# JWT Configuration
JWT_SECRET=your_super_secure_jwt_secret_key_here_minimum_32_characters
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Business Configuration
BUSINESS_ID=VMURUGAN_001
BUSINESS_NAME=VMUrugan Gold Trading
ADMIN_DEFAULT_PASSWORD=VMURUGAN_ADMIN_2025

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080,https://yourdomain.com

# Gold Price API Configuration
METALS_LIVE_API_KEY=your_metals_live_api_key
GOLD_PRICE_UPDATE_INTERVAL=300000

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/api.log

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
FROM_EMAIL=<EMAIL>

# SMS Configuration (Optional)
SMS_PROVIDER=twilio
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
TWILIO_PHONE_NUMBER=+**********

# File Upload Configuration
UPLOAD_MAX_SIZE=5242880
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,application/pdf

# Cache Configuration
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090
